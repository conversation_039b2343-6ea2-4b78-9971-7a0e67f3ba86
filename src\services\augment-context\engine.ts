import * as vscode from "vscode"
import * as path from "path"
import { promises as fs } from "fs"
import { getWorkspacePath } from "../../utils/path"
import { CodeIndexManager } from "../code-index/manager"
import { VectorStoreSearchResult } from "../code-index/interfaces"
import { GitCommit, searchCommits, getCommitInfo } from "../../utils/git"

export interface ContextRetrievalOptions {
	query: string
	maxResults?: number
	includeCommitHistory?: boolean
	directoryPrefix?: string
	contextWindowSize?: number
}

export interface ContextResult {
	type: "code" | "commit" | "file"
	content: string
	filePath?: string
	score?: number
	metadata?: Record<string, any>
}

export interface WorkspaceIndexingStatus {
	isIndexing: boolean
	totalFiles: number
	indexedFiles: number
	lastIndexed?: Date
	error?: string
}

/**
 * Enhanced context engine similar to Augment Code's capabilities
 * Provides workspace indexing, context retrieval, and historical awareness
 */
export class AugmentContextEngine {
	private codeIndexManager: CodeIndexManager | undefined
	private workspacePath: string
	private contextWindowSize: number
	private maxRetrievalResults: number
	private isInitialized: boolean = false

	constructor(
		contextWindowSize: number = 200_000,
		maxRetrievalResults: number = 50
	) {
		this.contextWindowSize = contextWindowSize
		this.maxRetrievalResults = maxRetrievalResults
		this.workspacePath = getWorkspacePath()
		// CodeIndexManager will be initialized in initialize() method
	}

	/**
	 * Initialize the context engine
	 */
	async initialize(): Promise<void> {
		if (this.isInitialized) {
			return
		}

		try {
			// Get CodeIndexManager instance (requires extension context)
			// For now, we'll skip code indexing if no extension context is available
			const context = (global as any).extensionContext
			if (context) {
				this.codeIndexManager = CodeIndexManager.getInstance(context)
				if (this.codeIndexManager) {
					// Initialize with a mock context proxy for now
					const mockContextProxy = {
						get: (key: string) => undefined,
						set: (key: string, value: any) => Promise.resolve(),
					} as any
					await this.codeIndexManager.initialize(mockContextProxy)
				}
			}

			// Start automatic workspace indexing if available
			if (this.codeIndexManager) {
				await this.startWorkspaceIndexing()
			}

			this.isInitialized = true
		} catch (error) {
			console.error("Failed to initialize AugmentContextEngine:", error)
			// Don't throw error if code indexing fails, continue without it
			this.isInitialized = true
		}
	}

	/**
	 * Start automatic workspace indexing
	 */
	private async startWorkspaceIndexing(): Promise<void> {
		if (!this.codeIndexManager) {
			console.warn("CodeIndexManager not available, skipping workspace indexing")
			return
		}

		try {
			// Start indexing with the existing configuration
			// The CodeIndexManager handles its own configuration from VS Code settings
			await this.codeIndexManager.startIndexing()
		} catch (error) {
			console.error("Failed to start workspace indexing:", error)
			// Don't throw error, continue without indexing
		}
	}

	/**
	 * Retrieve relevant context for a given query
	 */
	async retrieveContext(options: ContextRetrievalOptions): Promise<ContextResult[]> {
		if (!this.isInitialized) {
			await this.initialize()
		}

		const results: ContextResult[] = []
		const maxResults = options.maxResults || this.maxRetrievalResults

		try {
			// 1. Search code index for relevant code snippets
			const codeResults = await this.searchCodeIndex(options.query, options.directoryPrefix)
			
			// Convert code search results to context results
			for (const result of codeResults.slice(0, Math.floor(maxResults * 0.7))) {
				results.push({
					type: "code",
					content: result.payload?.codeChunk || "",
					filePath: result.payload?.filePath || "",
					score: result.score,
					metadata: {
						startLine: result.payload?.startLine || 0,
						endLine: result.payload?.endLine || 0,
						language: this.detectLanguage(result.payload?.filePath || ""),
					},
				})
			}

			// 2. Include commit history if requested
			if (options.includeCommitHistory) {
				const commitResults = await this.searchCommitHistory(options.query)
				
				for (const commit of commitResults.slice(0, Math.floor(maxResults * 0.3))) {
					const commitInfo = await getCommitInfo(commit.hash, this.workspacePath)
					results.push({
						type: "commit",
						content: commitInfo,
						metadata: {
							hash: commit.hash,
							shortHash: commit.shortHash,
							author: commit.author,
							date: commit.date,
							subject: commit.subject,
						},
					})
				}
			}

			// 3. Rank and filter results by relevance
			const rankedResults = this.rankResults(results, options.query)
			
			// 4. Ensure results fit within context window
			return this.fitToContextWindow(rankedResults, options.contextWindowSize || this.contextWindowSize)

		} catch (error) {
			console.error("Failed to retrieve context:", error)
			return []
		}
	}

	/**
	 * Search the code index for relevant snippets
	 */
	private async searchCodeIndex(query: string, directoryPrefix?: string): Promise<VectorStoreSearchResult[]> {
		if (!this.codeIndexManager) {
			return []
		}

		try {
			return await this.codeIndexManager.searchIndex(query, directoryPrefix)
		} catch (error) {
			console.error("Failed to search code index:", error)
			return []
		}
	}

	/**
	 * Search commit history for relevant changes
	 */
	private async searchCommitHistory(query: string): Promise<GitCommit[]> {
		try {
			return await searchCommits(query, this.workspacePath)
		} catch (error) {
			console.error("Failed to search commit history:", error)
			return []
		}
	}

	/**
	 * Rank results by relevance to the query
	 */
	private rankResults(results: ContextResult[], query: string): ContextResult[] {
		const queryTerms = query.toLowerCase().split(/\s+/)
		
		return results
			.map(result => {
				let relevanceScore = result.score || 0
				
				// Boost score based on query term matches in content
				const content = result.content.toLowerCase()
				const matchCount = queryTerms.reduce((count, term) => {
					return count + (content.split(term).length - 1)
				}, 0)
				
				relevanceScore += matchCount * 0.1
				
				// Boost recent commits
				if (result.type === "commit" && result.metadata?.date) {
					const commitDate = new Date(result.metadata.date)
					const daysSinceCommit = (Date.now() - commitDate.getTime()) / (1000 * 60 * 60 * 24)
					relevanceScore += Math.max(0, 1 - daysSinceCommit / 30) * 0.2
				}
				
				return { ...result, score: relevanceScore }
			})
			.sort((a, b) => (b.score || 0) - (a.score || 0))
	}

	/**
	 * Fit results to context window size
	 */
	private fitToContextWindow(results: ContextResult[], contextWindowSize: number): ContextResult[] {
		const fitted: ContextResult[] = []
		let currentSize = 0
		
		// Rough estimation: 4 characters per token
		const maxSize = contextWindowSize * 4
		
		for (const result of results) {
			const resultSize = result.content.length
			
			if (currentSize + resultSize <= maxSize) {
				fitted.push(result)
				currentSize += resultSize
			} else {
				// Try to fit a truncated version
				const remainingSpace = maxSize - currentSize
				if (remainingSpace > 100) {
					fitted.push({
						...result,
						content: result.content.substring(0, remainingSpace - 50) + "...",
					})
				}
				break
			}
		}
		
		return fitted
	}

	/**
	 * Detect programming language from file path
	 */
	private detectLanguage(filePath: string): string {
		const ext = path.extname(filePath).toLowerCase()
		const languageMap: Record<string, string> = {
			".ts": "typescript",
			".js": "javascript",
			".tsx": "tsx",
			".jsx": "jsx",
			".py": "python",
			".java": "java",
			".cpp": "cpp",
			".c": "c",
			".cs": "csharp",
			".go": "go",
			".rs": "rust",
			".php": "php",
			".rb": "ruby",
			".swift": "swift",
			".kt": "kotlin",
			".scala": "scala",
			".sh": "bash",
			".sql": "sql",
			".json": "json",
			".yaml": "yaml",
			".yml": "yaml",
			".xml": "xml",
			".html": "html",
			".css": "css",
			".scss": "scss",
			".less": "less",
			".md": "markdown",
		}
		
		return languageMap[ext] || "text"
	}

	/**
	 * Get current workspace indexing status
	 */
	getIndexingStatus(): WorkspaceIndexingStatus {
		if (!this.codeIndexManager) {
			return {
				isIndexing: false,
				totalFiles: 0,
				indexedFiles: 0,
				lastIndexed: undefined,
				error: "CodeIndexManager not available",
			}
		}

		const status = this.codeIndexManager.getCurrentStatus()

		return {
			isIndexing: status.systemStatus === "Indexing",
			totalFiles: status.totalItems || 0,
			indexedFiles: status.processedItems || 0,
			lastIndexed: undefined, // This property doesn't exist in the status
			error: status.systemStatus === "Error" ? status.message : undefined,
		}
	}

	/**
	 * Clear all indexed data
	 */
	async clearIndex(): Promise<void> {
		if (this.codeIndexManager) {
			await this.codeIndexManager.clearIndexData()
		}
	}

	/**
	 * Dispose of resources
	 */
	dispose(): void {
		// Clean up resources if needed
	}
}
