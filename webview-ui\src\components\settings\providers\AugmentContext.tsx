import { useCallback, useState } from "react"
import { Checkbox } from "vscrui"
import { VSCodeTextField, VSCodeDropdown, VSCodeOption } from "@vscode/webview-ui-toolkit/react"

import type { ProviderSettings } from "@roo-code/types"

import { useAppTranslation } from "@src/i18n/TranslationContext"
import { VSCodeButtonLink } from "@src/components/common/VSCodeButtonLink"

import { inputEventTransform, noTransform } from "../transforms"

type AugmentContextProps = {
	apiConfiguration: ProviderSettings
	setApiConfigurationField: (field: keyof ProviderSettings, value: ProviderSettings[keyof ProviderSettings]) => void
}

export const AugmentContext = ({ apiConfiguration, setApiConfigurationField }: AugmentContextProps) => {
	const { t } = useAppTranslation()

	const [coreDriverBaseUrlSelected, setCoreDriverBaseUrlSelected] = useState(!!apiConfiguration?.coreDriverBaseUrl)

	const handleInputChange = useCallback(
		<K extends keyof ProviderSettings, E>(
			field: K,
			transform: (event: E) => ProviderSettings[K] = inputEventTransform,
		) =>
			(event: E | Event) => {
				setApiConfigurationField(field, transform(event as E))
			},
		[setApiConfigurationField],
	)

	return (
		<>
			{/* Core Driver (Claude Sonnet 3.7) Configuration */}
			<div className="border border-vscode-widget-border rounded p-4 mb-4">
				<h3 className="text-lg font-medium mb-3">Core Driver (Claude Sonnet 3.7)</h3>
				
				<VSCodeTextField
					value={apiConfiguration?.coreDriverApiKey || ""}
					type="password"
					onInput={handleInputChange("coreDriverApiKey")}
					placeholder="sk-ant-..."
					className="w-full">
					<label className="block font-medium mb-1">Anthropic API Key</label>
				</VSCodeTextField>
				<div className="text-sm text-vscode-descriptionForeground -mt-2 mb-3">
					API key is stored securely and never sent to our servers
				</div>
				{!apiConfiguration?.coreDriverApiKey && (
					<VSCodeButtonLink href="https://console.anthropic.com/settings/keys" appearance="secondary">
						Get Anthropic API Key
					</VSCodeButtonLink>
				)}

				<div className="mt-3">
					<Checkbox
						checked={coreDriverBaseUrlSelected}
						onChange={(checked: boolean) => {
							setCoreDriverBaseUrlSelected(checked)
							if (!checked) {
								setApiConfigurationField("coreDriverBaseUrl", "")
							}
						}}>
						Use Custom Base URL
					</Checkbox>
					{coreDriverBaseUrlSelected && (
						<VSCodeTextField
							value={apiConfiguration?.coreDriverBaseUrl || ""}
							type="url"
							onInput={handleInputChange("coreDriverBaseUrl")}
							placeholder="https://api.anthropic.com"
							className="w-full mt-1"
						/>
					)}
				</div>
			</div>



			{/* Feature Configuration */}
			<div className="border border-vscode-widget-border rounded p-4 mb-4">
				<h3 className="text-lg font-medium mb-3">Features</h3>

				<Checkbox
					checked={apiConfiguration?.contextEngineEnabled ?? true}
					onChange={handleInputChange("contextEngineEnabled", noTransform)}
					className="mb-2">
					Enable Context Engine
				</Checkbox>
				<div className="text-sm text-vscode-descriptionForeground mb-3 ml-6">
					Provides workspace indexing and intelligent context retrieval
				</div>



				<Checkbox
					checked={apiConfiguration?.mcpIntegrationEnabled ?? true}
					onChange={handleInputChange("mcpIntegrationEnabled", noTransform)}
					className="mb-2">
					Enable MCP Integration
				</Checkbox>
				<div className="text-sm text-vscode-descriptionForeground mb-3 ml-6">
					Connects to Model Context Protocol servers for external tool integration
				</div>

				<Checkbox
					checked={apiConfiguration?.agentMemoriesEnabled ?? true}
					onChange={handleInputChange("agentMemoriesEnabled", noTransform)}
					className="mb-2">
					Enable Agent Memories
				</Checkbox>
				<div className="text-sm text-vscode-descriptionForeground ml-6">
					Personalizes responses based on your preferences and project history
				</div>
			</div>

			{/* Workspace Indexing Configuration */}
			{apiConfiguration?.contextEngineEnabled && (
				<div className="border border-vscode-widget-border rounded p-4 mb-4">
					<h3 className="text-lg font-medium mb-3">Workspace Indexing</h3>

					<Checkbox
						checked={apiConfiguration?.workspaceIndexingEnabled ?? true}
						onChange={handleInputChange("workspaceIndexingEnabled", noTransform)}
						className="mb-3">
						Enable Workspace Indexing
					</Checkbox>

					{apiConfiguration?.workspaceIndexingEnabled !== false && (
						<>
							<VSCodeTextField
								value={apiConfiguration?.contextWindowSize?.toString() || "200000"}
								onInput={handleInputChange("contextWindowSize", (e: any) => parseInt(e.target.value) || 200000)}
								className="w-full mb-3">
								<label className="block font-medium mb-1">Context Window Size (tokens)</label>
							</VSCodeTextField>
							<div className="text-sm text-vscode-descriptionForeground mb-3 -mt-2">
								Maximum number of tokens to include in context retrieval
							</div>

							<VSCodeTextField
								value={apiConfiguration?.maxRetrievalResults?.toString() || "50"}
								onInput={handleInputChange("maxRetrievalResults", (e: any) => parseInt(e.target.value) || 50)}
								className="w-full mb-3">
								<label className="block font-medium mb-1">Max Retrieval Results</label>
							</VSCodeTextField>
							<div className="text-sm text-vscode-descriptionForeground mb-3 -mt-2">
								Maximum number of code snippets to retrieve for context
							</div>

							<Checkbox
								checked={apiConfiguration?.commitHistoryEnabled ?? true}
								onChange={handleInputChange("commitHistoryEnabled", noTransform)}
								className="mb-2">
								Enable Commit History Indexing
							</Checkbox>
							<div className="text-sm text-vscode-descriptionForeground ml-6">
								Index and summarize commit history for historical context queries
							</div>
						</>
					)}
				</div>
			)}

			{/* Advanced Configuration */}
			<div className="border border-vscode-widget-border rounded p-4">
				<h3 className="text-lg font-medium mb-3">Advanced Settings</h3>

				<VSCodeTextField
					value={apiConfiguration?.coreDriverModelId || "claude-3-7-sonnet-20250219"}
					onInput={handleInputChange("coreDriverModelId")}
					className="w-full mb-3">
					<label className="block font-medium mb-1">Core Driver Model ID</label>
				</VSCodeTextField>
				<div className="text-sm text-vscode-descriptionForeground mb-3 -mt-2">
					Model ID for the core driver (Claude Sonnet 3.7)
				</div>



				<VSCodeTextField
					value={apiConfiguration?.commitSummaryModel || "claude-3-haiku-20240307"}
					onInput={handleInputChange("commitSummaryModel")}
					className="w-full">
					<label className="block font-medium mb-1">Commit Summary Model</label>
				</VSCodeTextField>
				<div className="text-sm text-vscode-descriptionForeground -mt-2">
					Lightweight model for summarizing commit messages and changes
				</div>
			</div>
		</>
	)
}
