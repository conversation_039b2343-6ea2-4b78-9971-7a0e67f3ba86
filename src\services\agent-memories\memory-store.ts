import * as vscode from "vscode"
import * as fs from "fs/promises"
import * as path from "path"
import { randomUUID } from "crypto"
import { getWorkspacePath } from "../../utils/path"
import {
	Memory,
	MemoryType,
	MemoryContext,
	MemoryScope,
	MemoryQuery,
	MemoryStats,
	PersonalizationProfile,
	LearningEvent,
} from "./types"

/**
 * Memory Store for persistent storage of agent memories
 */
export class MemoryStore {
	private workspacePath: string
	private memoriesPath: string
	private profilePath: string
	private memories: Map<string, Memory> = new Map()
	private profile: PersonalizationProfile | null = null
	private isLoaded: boolean = false

	constructor() {
		this.workspacePath = getWorkspacePath()
		const kilocodeDir = path.join(this.workspacePath, ".vscode", "kilocode")
		this.memoriesPath = path.join(kilocodeDir, "memories.json")
		this.profilePath = path.join(kilocodeDir, "profile.json")
	}

	/**
	 * Initialize the memory store
	 */
	async initialize(): Promise<void> {
		if (this.isLoaded) {
			return
		}

		try {
			// Ensure directory exists
			await fs.mkdir(path.dirname(this.memoriesPath), { recursive: true })

			// Load existing memories and profile
			await this.loadMemories()
			await this.loadProfile()

			this.isLoaded = true
		} catch (error) {
			console.error("Failed to initialize memory store:", error)
			throw error
		}
	}

	/**
	 * Load memories from disk
	 */
	private async loadMemories(): Promise<void> {
		try {
			const data = await fs.readFile(this.memoriesPath, "utf-8")
			const memoriesData = JSON.parse(data)

			this.memories.clear()
			for (const memoryData of memoriesData.memories || []) {
				const memory: Memory = {
					...memoryData,
					createdAt: new Date(memoryData.createdAt),
					lastAccessed: new Date(memoryData.lastAccessed),
				}
				this.memories.set(memory.id, memory)
			}

			console.log(`Loaded ${this.memories.size} memories`)
		} catch (error) {
			// File doesn't exist or is corrupted, start fresh
			console.log("Starting with empty memory store")
		}
	}

	/**
	 * Load personalization profile from disk
	 */
	private async loadProfile(): Promise<void> {
		try {
			const data = await fs.readFile(this.profilePath, "utf-8")
			const profileData = JSON.parse(data)

			this.profile = {
				...profileData,
				createdAt: new Date(profileData.createdAt),
				updatedAt: new Date(profileData.updatedAt),
				learningHistory: profileData.learningHistory?.map((event: any) => ({
					...event,
					timestamp: new Date(event.timestamp),
				})) || [],
			}

			console.log("Loaded personalization profile")
		} catch (error) {
			// Create new profile
			this.profile = {
				workspaceId: this.getWorkspaceId(),
				preferences: {},
				codingStyles: {},
				workflows: {},
				adaptationRules: [],
				learningHistory: [],
				createdAt: new Date(),
				updatedAt: new Date(),
			}
			console.log("Created new personalization profile")
		}
	}

	/**
	 * Save memories to disk
	 */
	private async saveMemories(): Promise<void> {
		try {
			const memoriesData = {
				version: "1.0",
				lastUpdated: new Date().toISOString(),
				memories: Array.from(this.memories.values()),
			}

			await fs.writeFile(this.memoriesPath, JSON.stringify(memoriesData, null, 2))
		} catch (error) {
			console.error("Failed to save memories:", error)
		}
	}

	/**
	 * Save personalization profile to disk
	 */
	private async saveProfile(): Promise<void> {
		if (!this.profile) return

		try {
			this.profile.updatedAt = new Date()
			await fs.writeFile(this.profilePath, JSON.stringify(this.profile, null, 2))
		} catch (error) {
			console.error("Failed to save profile:", error)
		}
	}

	/**
	 * Store a new memory
	 */
	async storeMemory(memory: Omit<Memory, "id" | "createdAt" | "lastAccessed" | "accessCount">): Promise<string> {
		const id = randomUUID()
		const fullMemory: Memory = {
			...memory,
			id,
			createdAt: new Date(),
			lastAccessed: new Date(),
			accessCount: 0,
		}

		this.memories.set(id, fullMemory)
		await this.saveMemories()

		// Record learning event
		await this.recordLearningEvent({
			type: "pattern_detected",
			data: { memoryType: memory.type, content: memory.content },
			context: memory.context,
			timestamp: new Date(),
		})

		return id
	}

	/**
	 * Retrieve memories by query
	 */
	async queryMemories(query: MemoryQuery): Promise<Memory[]> {
		let results = Array.from(this.memories.values())

		// Filter by type
		if (query.types && query.types.length > 0) {
			results = results.filter(memory => query.types!.includes(memory.type))
		}

		// Filter by context
		if (query.context) {
			results = results.filter(memory => this.matchesContext(memory.context, query.context!))
		}

		// Filter by tags
		if (query.tags && query.tags.length > 0) {
			results = results.filter(memory => 
				query.tags!.some(tag => memory.tags.includes(tag))
			)
		}

		// Filter by importance
		if (query.minImportance !== undefined) {
			results = results.filter(memory => memory.importance >= query.minImportance!)
		}

		// Filter by confidence
		if (query.minConfidence !== undefined) {
			results = results.filter(memory => memory.confidence >= query.minConfidence!)
		}

		// Text search
		if (query.textQuery) {
			const searchTerm = query.textQuery.toLowerCase()
			results = results.filter(memory => 
				memory.content.toLowerCase().includes(searchTerm) ||
				memory.tags.some(tag => tag.toLowerCase().includes(searchTerm))
			)
		}

		// Sort results
		const sortBy = query.sortBy || "importance"
		results.sort((a, b) => {
			switch (sortBy) {
				case "importance":
					return b.importance - a.importance
				case "confidence":
					return b.confidence - a.confidence
				case "recency":
					return b.lastAccessed.getTime() - a.lastAccessed.getTime()
				case "frequency":
					return b.accessCount - a.accessCount
				default:
					return 0
			}
		})

		// Update access counts
		for (const memory of results) {
			memory.lastAccessed = new Date()
			memory.accessCount++
		}

		// Apply limit
		if (query.limit) {
			results = results.slice(0, query.limit)
		}

		// Save updated access counts
		if (results.length > 0) {
			await this.saveMemories()
		}

		return results
	}

	/**
	 * Update an existing memory
	 */
	async updateMemory(id: string, updates: Partial<Memory>): Promise<boolean> {
		const memory = this.memories.get(id)
		if (!memory) {
			return false
		}

		Object.assign(memory, updates)
		memory.lastAccessed = new Date()
		
		await this.saveMemories()
		return true
	}

	/**
	 * Delete a memory
	 */
	async deleteMemory(id: string): Promise<boolean> {
		const deleted = this.memories.delete(id)
		if (deleted) {
			await this.saveMemories()
		}
		return deleted
	}

	/**
	 * Get memory statistics
	 */
	getStats(): MemoryStats {
		const memories = Array.from(this.memories.values())
		
		const memoriesByType: Record<MemoryType, number> = {} as any
		const memoriesByScope: Record<MemoryScope, number> = {} as any
		const tagCounts: Record<string, number> = {}

		for (const memory of memories) {
			memoriesByType[memory.type] = (memoriesByType[memory.type] || 0) + 1
			memoriesByScope[memory.context.scope] = (memoriesByScope[memory.context.scope] || 0) + 1
			
			for (const tag of memory.tags) {
				tagCounts[tag] = (tagCounts[tag] || 0) + 1
			}
		}

		const totalImportance = memories.reduce((sum, m) => sum + m.importance, 0)
		const totalConfidence = memories.reduce((sum, m) => sum + m.confidence, 0)

		const mostAccessedMemories = memories
			.sort((a, b) => b.accessCount - a.accessCount)
			.slice(0, 10)

		const recentMemories = memories
			.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime())
			.slice(0, 10)

		const topTags = Object.entries(tagCounts)
			.sort(([, a], [, b]) => b - a)
			.slice(0, 10)
			.map(([tag, count]) => ({ tag, count }))

		return {
			totalMemories: memories.length,
			memoriesByType,
			memoriesByScope,
			averageImportance: memories.length > 0 ? totalImportance / memories.length : 0,
			averageConfidence: memories.length > 0 ? totalConfidence / memories.length : 0,
			mostAccessedMemories,
			recentMemories,
			topTags,
		}
	}

	/**
	 * Record a learning event
	 */
	async recordLearningEvent(event: LearningEvent): Promise<void> {
		if (!this.profile) return

		this.profile.learningHistory.push(event)
		
		// Keep only last 1000 events
		if (this.profile.learningHistory.length > 1000) {
			this.profile.learningHistory = this.profile.learningHistory.slice(-1000)
		}

		await this.saveProfile()
	}

	/**
	 * Get personalization profile
	 */
	getProfile(): PersonalizationProfile | null {
		return this.profile
	}

	/**
	 * Update personalization profile
	 */
	async updateProfile(updates: Partial<PersonalizationProfile>): Promise<void> {
		if (!this.profile) return

		Object.assign(this.profile, updates)
		await this.saveProfile()
	}

	/**
	 * Check if memory context matches query context
	 */
	private matchesContext(memoryContext: MemoryContext, queryContext: Partial<MemoryContext>): boolean {
		for (const [key, value] of Object.entries(queryContext)) {
			if (value !== undefined && (memoryContext as any)[key] !== value) {
				return false
			}
		}
		return true
	}

	/**
	 * Get workspace ID
	 */
	private getWorkspaceId(): string {
		return path.basename(this.workspacePath)
	}

	/**
	 * Get all memories as an array
	 */
	getMemories(): Memory[] {
		return Array.from(this.memories.values())
	}

	/**
	 * Clear all memories (for testing/reset)
	 */
	async clearMemories(): Promise<void> {
		this.memories.clear()
		await this.saveMemories()
	}
}
