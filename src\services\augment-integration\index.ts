/**
 * Augment Code Integration
 * 
 * This module provides a comprehensive integration similar to Augment Code's capabilities:
 * 
 * 1. <PERSON>net 3.7 as Core Driver + OpenAI o1 as Ensembler
 * 2. Workspace Indexing & Context Retrieval (200k token context window)
 * 3. Context Lineage with Historical Awareness (real-time commit indexing)
 * 4. Model Context Protocol (MCP) Integration
 * 5. Intelligent Model Routing & Scaling
 * 6. Agent Memories System for Personalization
 */

export { Augment<PERSON>ontextHandler } from "../../api/providers/augment-context"

// Core Context Engine
export {
	AugmentContextEngine,
	CommitLineageService,
	CommitIndexer,
	AugmentContextManager,
} from "../augment-context"

export type {
	ContextRetrievalOptions,
	ContextResult,
	WorkspaceIndexingStatus,
	CommitSummary,
	CommitLineageOptions,
	CommitIndexEntry,
	CommitIndexOptions,
	EnhancedContextOptions,
	EnhancedContextResult,
} from "../augment-context"

// MCP Integration
export {
	MCPClient,
	MCPManager,
	MCPIntegrationService,
} from "../mcp"

export type {
	MCPServer,
	MCPTool,
	MCPResource,
	MCPPrompt,
	MCPToolCall,
	MCPToolResult,
	MCPServerConfig,
	MCPServerStatus,
	MCPIntegrationStats,
	MCPToolWithCategory,
	MCPIntegrationOptions,
} from "../mcp"

export { MCPToolCategory, BUILTIN_MCP_SERVERS } from "../mcp"

// Intelligent Routing
export {
	TaskAnalyzer,
	ModelRouter,
	RoutingEngine,
} from "../intelligent-routing"

export type {
	TaskMetadata,
	LanguageFrameworkInfo,
	ModelCapabilities,
	ModelOption,
	RoutingDecision,
	RoutingRequest,
	RoutingPreferences,
	RoutingResult,
	ExecutionPlan,
} from "../intelligent-routing"

export { TaskType } from "../intelligent-routing"

// Agent Memories
export {
	MemoryStore,
	MemoryManager,
} from "../agent-memories"

export type {
	Memory,
	MemoryContext,
	MemoryQuery,
	MemoryInsight,
	MemoryStats,
	LearningEvent,
	AdaptationRule,
	PersonalizationProfile,
	CodingStyleMemory,
	PreferenceMemory,
	WorkflowMemory,
	FeedbackMemory,
	LearningOptions,
} from "../agent-memories"

export {
	MemoryType,
	MemoryScope,
	MEMORY_TEMPLATES,
	DEFAULT_ADAPTATION_RULES,
} from "../agent-memories"

/**
 * Usage Summary:
 * 
 * ## 1. API Configuration
 * Add "augment-context" as your API provider in settings:
 * ```json
 * {
 *   "apiProvider": "augment-context",
 *   "coreDriverApiKey": "your-claude-api-key",
 *   "contextEngineEnabled": true,
 *   "workspaceIndexingEnabled": true,
 *   "commitHistoryEnabled": true,
 *   "mcpIntegrationEnabled": true,
 *   "agentMemoriesEnabled": true,
 *   "contextWindowSize": 200000,
 *   "maxRetrievalResults": 50
 * }
 * ```
 * 
 * ## 2. Features Overview
 * 
 * ### Workspace Indexing & Context Retrieval
 * - Automatic indexing of your entire codebase
 * - 200,000-token context window support
 * - Real-time retrieval of relevant code snippets
 * - Cross-repo understanding capability
 * 
 * ### Context Lineage: Historical Awareness
 * - Real-time commit indexing from Git
 * - LLM-summarized commits using Gemini 2.0 Flash
 * - Historical context injection for "why was this introduced" queries
 * - Commit retrieval based on file changes and patterns
 * 
 * ### Model Context Protocol (MCP) Integration
 * - GitHub integration for repository operations
 * - File system access for workspace operations
 * - Web search capabilities via Brave Search
 * - Database integrations (PostgreSQL, SQLite, MongoDB)
 * - Extensible custom server support
 * 
 * ### Intelligent Model Routing & Scaling
 * - Task analysis with language/framework detection
 * - Automatic model selection based on complexity
 * - Claude Sonnet 3.7 for general tasks
 * - OpenAI o1 for complex reasoning tasks
 * - Cost and latency optimization
 * 
 * ### Agent Memories System
 * - Personalization based on user preferences
 * - Coding style learning and adaptation
 * - Project-specific convention detection
 * - Workflow pattern recognition
 * - Adaptive behavior based on feedback
 * 
 * ## 3. Integration Points
 * 
 * The system integrates at multiple levels:
 * - Provider level: New "augment-context" API provider
 * - Context level: Enhanced prompts with workspace and historical context
 * - Routing level: Intelligent model selection
 * - Learning level: Continuous adaptation to user patterns
 * 
 * ## 4. Data Storage
 * 
 * All data is stored locally in your workspace:
 * - `.vscode/commit-index.json` - Commit index cache
 * - `.vscode/kilocode/memories.json` - Agent memories
 * - `.vscode/kilocode/profile.json` - Personalization profile
 * 
 * ## 5. Performance Characteristics
 * 
 * - Context retrieval: ~100-500ms depending on codebase size
 * - Commit indexing: Real-time with 30-second intervals
 * - Model routing: ~10-50ms for decision making
 * - Memory operations: ~1-10ms for queries
 * 
 * ## 6. Extensibility
 * 
 * The system is designed to be extensible:
 * - Add custom MCP servers for new integrations
 * - Extend memory types for new learning patterns
 * - Add new routing criteria for model selection
 * - Customize adaptation rules for specific workflows
 */
